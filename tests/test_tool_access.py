#!/usr/bin/env python3
"""
Tool Access Testing
Tests accessibility and basic functionality of penetration testing tools
"""

import subprocess
import json
import os
import sys
from pathlib import Path

class ToolAccessTester:
    def __init__(self):
        self.results = {
            'accessible': [],
            'inaccessible': [],
            'permission_issues': [],
            'functional': [],
            'non_functional': []
        }
        
        # Load tool inventory
        try:
            with open('docs/tool_inventory.json', 'r') as f:
                self.tools = json.load(f)
        except FileNotFoundError:
            print("[-] Tool inventory not found. Run tool_inventory.py first.")
            sys.exit(1)
            
    def test_tool_access(self, tool_path):
        """Test if tool is accessible and executable"""
        try:
            if not os.path.exists(tool_path):
                return False, "File does not exist"
                
            if not os.access(tool_path, os.X_OK):
                return False, "No execute permission"
                
            return True, "Accessible"
        except Exception as e:
            return False, f"Error: {str(e)}"
            
    def test_tool_functionality(self, tool_name, tool_path):
        """Test basic functionality of tool"""
        try:
            # Test with help flag (most common)
            test_commands = [
                [tool_path, '--help'],
                [tool_path, '-h'],
                [tool_path, '--version'],
                [tool_path, '-v']
            ]
            
            for cmd in test_commands:
                try:
                    result = subprocess.run(cmd, capture_output=True, 
                                          text=True, timeout=10)
                    if result.returncode == 0 or "usage" in result.stdout.lower() or "help" in result.stdout.lower():
                        return True, "Functional"
                except subprocess.TimeoutExpired:
                    continue
                except Exception:
                    continue
                    
            return False, "No response to standard flags"
            
        except Exception as e:
            return False, f"Error testing functionality: {str(e)}"
            
    def run_tests(self):
        """Run comprehensive tool access tests"""
        print("[+] Starting tool access tests...")
        
        total_tools = 0
        for category, tools in self.tools.items():
            print(f"\n[+] Testing {category} tools:")
            
            for tool_name, tool_path in tools.items():
                total_tools += 1
                print(f"    Testing {tool_name}...", end=" ")
                
                # Test accessibility
                accessible, access_msg = self.test_tool_access(tool_path)
                
                if accessible:
                    self.results['accessible'].append(tool_name)
                    print("✓ Accessible", end=" ")
                    
                    # Test functionality
                    functional, func_msg = self.test_tool_functionality(tool_name, tool_path)
                    
                    if functional:
                        self.results['functional'].append(tool_name)
                        print("✓ Functional")
                    else:
                        self.results['non_functional'].append(tool_name)
                        print(f"✗ Non-functional ({func_msg})")
                        
                else:
                    self.results['inaccessible'].append(tool_name)
                    if "permission" in access_msg.lower():
                        self.results['permission_issues'].append(tool_name)
                    print(f"✗ Inaccessible ({access_msg})")
                    
        return total_tools
        
    def print_summary(self, total_tools):
        """Print test summary"""
        print("\n" + "="*60)
        print("TOOL ACCESS TEST SUMMARY")
        print("="*60)
        
        accessible_count = len(self.results['accessible'])
        functional_count = len(self.results['functional'])
        
        print(f"Total tools tested: {total_tools}")
        print(f"Accessible tools: {accessible_count} ({accessible_count/total_tools*100:.1f}%)")
        print(f"Functional tools: {functional_count} ({functional_count/total_tools*100:.1f}%)")
        print(f"Permission issues: {len(self.results['permission_issues'])}")
        
        if self.results['inaccessible']:
            print(f"\nInaccessible tools ({len(self.results['inaccessible'])}):")
            for tool in self.results['inaccessible']:
                print(f"  - {tool}")
                
        if self.results['permission_issues']:
            print(f"\nPermission issues ({len(self.results['permission_issues'])}):")
            for tool in self.results['permission_issues']:
                print(f"  - {tool}")
                
        print(f"\nFully functional tools ({functional_count}):")
        for tool in self.results['functional']:
            print(f"  ✓ {tool}")
            
    def save_results(self, filename='test_results/tool_access_test.json'):
        """Save test results to file"""
        os.makedirs('test_results', exist_ok=True)
        
        with open(filename, 'w') as f:
            json.dump(self.results, f, indent=2)
            
        print(f"\n[+] Test results saved to {filename}")

if __name__ == "__main__":
    tester = ToolAccessTester()
    total = tester.run_tests()
    tester.print_summary(total)
    tester.save_results()
