#!/usr/bin/env python3
"""
Phase 1 Complete Verification
Comprehensive verification of ALL Phase 1 components
"""

import os
import subprocess
import sqlite3
import json
import time
import threading
import requests
from pathlib import Path

class Phase1Verifier:
    def __init__(self):
        self.results = {}
        
    def verify_checkpoint_1_1(self):
        """Verify Checkpoint 1.1: Development Environment"""
        print("🔍 CHECKPOINT 1.1: DEVELOPMENT ENVIRONMENT")
        print("="*50)
        
        # 1. Project directory structure
        required_dirs = [
            'worm/core', 'worm/c2', 'worm/loaders', 'worm/payloads',
            'worm/tools', 'worm/evasion', 'worm/persistence', 'worm/social',
            'worm/network', 'worm/web', 'worm/stealth', 'worm/exfiltration',
            'worm/cleanup', 'worm/tests', 'worm/docs', 'worm/scripts'
        ]
        
        missing_dirs = [d for d in required_dirs if not os.path.exists(d)]
        if missing_dirs:
            print(f"❌ Missing directories: {missing_dirs}")
            self.results['project_structure'] = False
        else:
            print("✅ Project directory structure complete")
            self.results['project_structure'] = True
            
        # 2. Git repository
        if os.path.exists('.git') and os.path.exists('.gitignore'):
            print("✅ Git repository initialized with .gitignore")
            self.results['git_setup'] = True
        else:
            print("❌ Git repository setup incomplete")
            self.results['git_setup'] = False
            
        # 3. Virtual environment
        if os.path.exists('venv/bin/python3') or os.path.exists('venv/Scripts/python.exe'):
            print("✅ Python virtual environment created")
            self.results['venv'] = True
        else:
            print("❌ Virtual environment missing")
            self.results['venv'] = False
            
        # 4. Tool inventory
        if os.path.exists('docs/tool_inventory.json'):
            try:
                with open('docs/tool_inventory.json', 'r') as f:
                    inventory = json.load(f)
                tool_count = sum(len(tools) for tools in inventory.values())
                print(f"✅ Tool inventory complete ({tool_count} tools)")
                self.results['tool_inventory'] = True
            except Exception as e:
                print(f"❌ Tool inventory error: {e}")
                self.results['tool_inventory'] = False
        else:
            print("❌ Tool inventory missing")
            self.results['tool_inventory'] = False
            
        # 5. Tool accessibility test
        critical_tools = ['nmap', 'masscan', 'hydra', 'crackmapexec', 'msfconsole']
        accessible_tools = []
        
        for tool in critical_tools:
            try:
                result = subprocess.run(['which', tool], capture_output=True, timeout=5)
                if result.returncode == 0:
                    accessible_tools.append(tool)
            except:
                pass
                
        if len(accessible_tools) >= 4:  # At least 4 out of 5 critical tools
            print(f"✅ Critical tools accessible ({len(accessible_tools)}/{len(critical_tools)})")
            self.results['tool_access'] = True
        else:
            print(f"❌ Insufficient tool access ({len(accessible_tools)}/{len(critical_tools)})")
            self.results['tool_access'] = False
            
        # 6. Python environment functionality
        try:
            import sqlite3, json, threading, subprocess
            print("✅ Python environment functional")
            self.results['python_env'] = True
        except ImportError as e:
            print(f"❌ Python environment issue: {e}")
            self.results['python_env'] = False
            
    def verify_checkpoint_1_2(self):
        """Verify Checkpoint 1.2: C2 Infrastructure"""
        print("\n🔍 CHECKPOINT 1.2: C2 INFRASTRUCTURE")
        print("="*50)
        
        # 1. Tor service availability
        try:
            result = subprocess.run(['which', 'tor'], capture_output=True, timeout=5)
            if result.returncode == 0:
                print("✅ Tor binary available")
                self.results['tor_binary'] = True
            else:
                print("❌ Tor binary not found")
                self.results['tor_binary'] = False
        except:
            print("❌ Tor check failed")
            self.results['tor_binary'] = False
            
        # 2. Sliver C2 server
        try:
            result = subprocess.run(['which', 'sliver-server'], capture_output=True, timeout=5)
            if result.returncode == 0:
                print("✅ Sliver C2 server available")
                self.results['sliver'] = True
            else:
                print("❌ Sliver C2 server not found")
                self.results['sliver'] = False
        except:
            print("❌ Sliver check failed")
            self.results['sliver'] = False
            
        # 3. Empire PowerShell framework
        try:
            result = subprocess.run(['which', 'powershell-empire'], capture_output=True, timeout=5)
            if result.returncode == 0:
                print("✅ PowerShell Empire available")
                self.results['empire'] = True
            else:
                print("❌ PowerShell Empire not found")
                self.results['empire'] = False
        except:
            print("❌ Empire check failed")
            self.results['empire'] = False
            
        # 4. SQLite database
        if os.path.exists('c2/worm_c2.db'):
            try:
                conn = sqlite3.connect('c2/worm_c2.db')
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                conn.close()
                
                required_tables = ['victims', 'commands', 'credentials', 'network_discoveries', 'social_accounts']
                if all(table in tables for table in required_tables):
                    print(f"✅ SQLite database complete ({len(tables)} tables)")
                    self.results['database'] = True
                else:
                    print("❌ Database missing required tables")
                    self.results['database'] = False
            except Exception as e:
                print(f"❌ Database error: {e}")
                self.results['database'] = False
        else:
            print("❌ Database file missing")
            self.results['database'] = False
            
        # 5. C2 server components
        c2_files = ['c2/server.py', 'c2/database.py', 'c2/tor_setup.py']
        missing_files = [f for f in c2_files if not os.path.exists(f)]
        
        if not missing_files:
            print("✅ C2 server components complete")
            self.results['c2_components'] = True
        else:
            print(f"❌ Missing C2 components: {missing_files}")
            self.results['c2_components'] = False
            
        # 6. Test C2 server startup
        try:
            # Import and test basic functionality
            import sys
            sys.path.append('.')
            from c2.database import C2Database
            
            db = C2Database()
            stats = db.get_statistics()
            
            print("✅ C2 database operations functional")
            self.results['c2_functional'] = True
            
        except Exception as e:
            print(f"❌ C2 functionality test failed: {e}")
            self.results['c2_functional'] = False
            
    def verify_payload_hosting(self):
        """Verify payload hosting capability"""
        print("\n🔍 PAYLOAD HOSTING VERIFICATION")
        print("="*30)
        
        # Create test payload directory
        os.makedirs('payloads', exist_ok=True)
        
        # Create test payload
        test_payload = '''#!/usr/bin/env python3
print("Test payload executed successfully")
'''
        
        with open('payloads/test_payload.py', 'w') as f:
            f.write(test_payload)
            
        if os.path.exists('payloads/test_payload.py'):
            print("✅ Payload hosting directory functional")
            self.results['payload_hosting'] = True
        else:
            print("❌ Payload hosting setup failed")
            self.results['payload_hosting'] = False
            
    def test_tor_connectivity(self):
        """Test Tor connectivity (basic)"""
        print("\n🔍 TOR CONNECTIVITY TEST")
        print("="*25)
        
        try:
            # Test if we can start Tor (without actually running it)
            result = subprocess.run(['tor', '--version'], capture_output=True, timeout=10)
            if result.returncode == 0 and 'Tor' in result.stdout.decode():
                print("✅ Tor version check successful")
                self.results['tor_connectivity'] = True
            else:
                print("❌ Tor version check failed")
                self.results['tor_connectivity'] = False
        except Exception as e:
            print(f"❌ Tor connectivity test failed: {e}")
            self.results['tor_connectivity'] = False
            
    def generate_final_report(self):
        """Generate final verification report"""
        print("\n" + "="*60)
        print("🎯 PHASE 1 COMPLETE VERIFICATION REPORT")
        print("="*60)
        
        checkpoint_1_1_tests = [
            'project_structure', 'git_setup', 'venv', 'tool_inventory', 
            'tool_access', 'python_env'
        ]
        
        checkpoint_1_2_tests = [
            'tor_binary', 'sliver', 'empire', 'database', 
            'c2_components', 'c2_functional'
        ]
        
        additional_tests = ['payload_hosting', 'tor_connectivity']
        
        print("\n📋 CHECKPOINT 1.1: DEVELOPMENT ENVIRONMENT")
        c1_1_passed = 0
        for test in checkpoint_1_1_tests:
            status = "✅ PASS" if self.results.get(test, False) else "❌ FAIL"
            print(f"  {test.replace('_', ' ').title():<25} {status}")
            if self.results.get(test, False):
                c1_1_passed += 1
                
        print(f"\n  Checkpoint 1.1 Score: {c1_1_passed}/{len(checkpoint_1_1_tests)} ({c1_1_passed/len(checkpoint_1_1_tests)*100:.1f}%)")
        
        print("\n📋 CHECKPOINT 1.2: C2 INFRASTRUCTURE")
        c1_2_passed = 0
        for test in checkpoint_1_2_tests:
            status = "✅ PASS" if self.results.get(test, False) else "❌ FAIL"
            print(f"  {test.replace('_', ' ').title():<25} {status}")
            if self.results.get(test, False):
                c1_2_passed += 1
                
        print(f"\n  Checkpoint 1.2 Score: {c1_2_passed}/{len(checkpoint_1_2_tests)} ({c1_2_passed/len(checkpoint_1_2_tests)*100:.1f}%)")
        
        print("\n📋 ADDITIONAL VERIFICATION")
        additional_passed = 0
        for test in additional_tests:
            status = "✅ PASS" if self.results.get(test, False) else "❌ FAIL"
            print(f"  {test.replace('_', ' ').title():<25} {status}")
            if self.results.get(test, False):
                additional_passed += 1
                
        total_tests = len(checkpoint_1_1_tests) + len(checkpoint_1_2_tests) + len(additional_tests)
        total_passed = c1_1_passed + c1_2_passed + additional_passed
        
        print(f"\n🎯 OVERALL PHASE 1 SCORE: {total_passed}/{total_tests} ({total_passed/total_tests*100:.1f}%)")
        
        if total_passed == total_tests:
            print("\n🎉 PHASE 1 VERIFICATION: 100% COMPLETE")
            print("✅ ALL SYSTEMS OPERATIONAL")
            print("🚀 READY FOR PHASE 2: UNIVERSAL LOADER DEVELOPMENT")
            return True
        else:
            print(f"\n⚠️  PHASE 1 VERIFICATION: {total_tests - total_passed} ISSUES FOUND")
            print("❌ RESOLVE ISSUES BEFORE PROCEEDING TO PHASE 2")
            return False
            
    def run_complete_verification(self):
        """Run complete Phase 1 verification"""
        print("🔍 STARTING COMPREHENSIVE PHASE 1 VERIFICATION")
        print("="*60)
        
        self.verify_checkpoint_1_1()
        self.verify_checkpoint_1_2()
        self.verify_payload_hosting()
        self.test_tor_connectivity()
        
        return self.generate_final_report()

if __name__ == "__main__":
    verifier = Phase1Verifier()
    success = verifier.run_complete_verification()
    
    if success:
        print("\n✅ Phase 1 verification completed successfully!")
        exit(0)
    else:
        print("\n❌ Phase 1 verification found issues!")
        exit(1)
