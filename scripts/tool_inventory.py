#!/usr/bin/env python3
"""
Tool Inventory Scanner
Scans system for available penetration testing tools
Creates comprehensive capability matrix
"""

import os
import subprocess
import json
from pathlib import Path

class ToolInventory:
    def __init__(self):
        self.tools = {
            'scanning': {},
            'exploitation': {},
            'lateral_movement': {},
            'credential_attacks': {},
            'web_attacks': {},
            'pivoting': {},
            'persistence': {},
            'post_exploitation': {},
            'shells': {},
            'social_engineering': {},
            'evasion': {},
            'stealth': {}
        }
        
        # Define tool categories and expected tools
        self.tool_definitions = {
            'scanning': [
                'nmap', 'masscan', 'autorecon', 'enum4linux', 'enum4linux-ng',
                'smtp-user-enum', 'snmpwalk', 'dnsrecon', 'ike-scan', 'enumiax'
            ],
            'exploitation': [
                'msfconsole', 'msfvenom', 'searchsploit', 'exploitdb', 'sqlmap',
                'nikto', 'metasploit-framework'
            ],
            'lateral_movement': [
                'impacket-psexec', 'impacket-wmiexec', 'impacket-smbexec',
                'impacket-dcomexec', 'impacket-atexec', 'impacket-secretsdump',
                'crackmapexec', 'evil-winrm'
            ],
            'credential_attacks': [
                'hydra', 'medusa', 'patator', 'ncrack', 'hashcat', 'john',
                'kerbrute', 'responder'
            ],
            'web_attacks': [
                'gobuster', 'dirb', 'dirbuster', 'davtest', 'uniscan',
                'joomscan', 'wpscan', 'ffuf', 'commix', 'xsser'
            ],
            'pivoting': [
                'chisel', 'ligolo-proxy', 'ligolo-agent', 'socat', 'sshuttle',
                'proxychains4', 'netcat', 'nc', 'ncat'
            ],
            'persistence': [
                'sliver', 'powershell-empire', 'beef-xss', 'mimikatz', 'rubeus'
            ],
            'post_exploitation': [
                'linpeas', 'winpeas', 'bloodhound', 'sharphound', 'bloodhound.py'
            ],
            'shells': [
                'netcat', 'nc', 'ncat', 'socat', 'pwncat', 'hoaxshell', 'shellnoob'
            ],
            'social_engineering': [
                'gophish', 'social-engineer-toolkit', 'king-phisher', 'evilginx2',
                'modlishka', 'zphisher', 'swaks', 'sendemail'
            ],
            'evasion': [
                'veil', 'shellter', 'unicorn', 'invoke-obfuscation', 'donut', 'scarecrow'
            ],
            'stealth': [
                'timestomp', 'shred', 'bleachbit', 'wevtutil', 'sdelete'
            ]
        }
        
    def check_tool_availability(self, tool_name):
        """Check if a tool is available on the system"""
        try:
            result = subprocess.run(['which', tool_name], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                # Try alternative locations
                common_paths = [
                    f'/usr/bin/{tool_name}',
                    f'/usr/sbin/{tool_name}',
                    f'/usr/local/bin/{tool_name}',
                    f'/opt/{tool_name}',
                    f'/usr/share/{tool_name}'
                ]
                
                for path in common_paths:
                    if os.path.exists(path) and os.access(path, os.X_OK):
                        return path
                        
                return None
        except:
            return None
            
    def get_tool_version(self, tool_path):
        """Get version information for a tool"""
        try:
            # Try common version flags
            version_flags = ['--version', '-v', '-V', 'version', '--help']
            
            for flag in version_flags:
                try:
                    result = subprocess.run([tool_path, flag], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0 and result.stdout:
                        return result.stdout.split('\n')[0][:100]  # First line, max 100 chars
                except:
                    continue
                    
            return "Version unknown"
        except:
            return "Version unknown"
            
    def scan_system(self):
        """Scan system for all available tools"""
        print("[+] Starting comprehensive tool inventory scan...")
        
        for category, tool_list in self.tool_definitions.items():
            print(f"[+] Scanning {category} tools...")
            
            for tool in tool_list:
                tool_path = self.check_tool_availability(tool)
                
                if tool_path:
                    version = self.get_tool_version(tool_path)
                    self.tools[category][tool] = {
                        'path': tool_path,
                        'version': version,
                        'available': True
                    }
                    print(f"    [✓] {tool} -> {tool_path}")
                else:
                    self.tools[category][tool] = {
                        'path': None,
                        'version': None,
                        'available': False
                    }
                    print(f"    [✗] {tool} -> Not found")
                    
    def scan_additional_tools(self):
        """Scan for additional tools that might be available"""
        print("[+] Scanning for additional penetration testing tools...")
        
        # Search common directories for additional tools
        search_dirs = ['/usr/bin', '/usr/sbin', '/usr/local/bin']
        additional_tools = set()
        
        for search_dir in search_dirs:
            if os.path.exists(search_dir):
                try:
                    for file in os.listdir(search_dir):
                        file_path = os.path.join(search_dir, file)
                        if os.access(file_path, os.X_OK):
                            # Look for tools with penetration testing keywords
                            keywords = ['crack', 'brute', 'scan', 'enum', 'exploit', 
                                      'hack', 'pen', 'test', 'attack', 'payload']
                            
                            if any(keyword in file.lower() for keyword in keywords):
                                additional_tools.add((file, file_path))
                except:
                    continue
                    
        # Add additional tools to miscellaneous category
        if additional_tools:
            self.tools['miscellaneous'] = {}
            for tool_name, tool_path in additional_tools:
                if not any(tool_name in category_tools for category_tools in self.tools.values() if isinstance(category_tools, dict)):
                    version = self.get_tool_version(tool_path)
                    self.tools['miscellaneous'][tool_name] = {
                        'path': tool_path,
                        'version': version,
                        'available': True
                    }
                    
    def generate_capability_matrix(self):
        """Generate capability matrix based on available tools"""
        capabilities = {
            'network_discovery': 0,
            'vulnerability_assessment': 0,
            'credential_attacks': 0,
            'lateral_movement': 0,
            'web_exploitation': 0,
            'social_engineering': 0,
            'persistence': 0,
            'evasion': 0,
            'post_exploitation': 0
        }
        
        # Calculate capability scores based on available tools
        for category, tools in self.tools.items():
            if isinstance(tools, dict):
                available_count = sum(1 for tool in tools.values() if tool.get('available', False))
                total_count = len(tools)
                
                if total_count > 0:
                    capability_score = (available_count / total_count) * 100
                    
                    # Map categories to capabilities
                    category_mapping = {
                        'scanning': 'network_discovery',
                        'exploitation': 'vulnerability_assessment',
                        'credential_attacks': 'credential_attacks',
                        'lateral_movement': 'lateral_movement',
                        'web_attacks': 'web_exploitation',
                        'social_engineering': 'social_engineering',
                        'persistence': 'persistence',
                        'evasion': 'evasion',
                        'post_exploitation': 'post_exploitation'
                    }
                    
                    if category in category_mapping:
                        capabilities[category_mapping[category]] = capability_score
                        
        return capabilities
        
    def save_inventory(self, filename='tool_inventory.json'):
        """Save tool inventory to JSON file"""
        inventory_data = {
            'tools': self.tools,
            'capabilities': self.generate_capability_matrix(),
            'summary': {
                'total_categories': len(self.tools),
                'total_tools_expected': sum(len(tools) for tools in self.tool_definitions.values()),
                'total_tools_available': sum(
                    sum(1 for tool in category_tools.values() if tool.get('available', False))
                    for category_tools in self.tools.values()
                    if isinstance(category_tools, dict)
                )
            }
        }
        
        with open(filename, 'w') as f:
            json.dump(inventory_data, f, indent=2)
            
        print(f"[+] Tool inventory saved to {filename}")
        
    def print_summary(self):
        """Print summary of tool availability"""
        print("\n" + "="*60)
        print("TOOL INVENTORY SUMMARY")
        print("="*60)
        
        for category, tools in self.tools.items():
            if isinstance(tools, dict):
                available = sum(1 for tool in tools.values() if tool.get('available', False))
                total = len(tools)
                percentage = (available / total * 100) if total > 0 else 0
                
                print(f"{category.upper():<20} {available:>3}/{total:<3} ({percentage:>5.1f}%)")
                
        capabilities = self.generate_capability_matrix()
        print("\nCAPABILITY MATRIX:")
        print("-" * 40)
        for capability, score in capabilities.items():
            print(f"{capability.replace('_', ' ').title():<25} {score:>5.1f}%")

if __name__ == "__main__":
    inventory = ToolInventory()
    inventory.scan_system()
    inventory.scan_additional_tools()
    inventory.save_inventory('docs/tool_inventory.json')
    inventory.print_summary()
