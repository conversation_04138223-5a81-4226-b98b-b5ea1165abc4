{"scanning": {"nmap": "/usr/bin/nmap", "masscan": "/usr/bin/masscan", "autorecon": "/usr/bin/autorecon", "enum4linux": "/usr/bin/enum4linux", "enum4linux-ng": "/usr/bin/enum4linux-ng", "smtp-user-enum": "/usr/bin/smtp-user-enum", "snmpwalk": "/usr/bin/snmpwalk", "dnsrecon": "/usr/bin/dnsrecon", "ike-scan": "/usr/bin/ike-scan", "enumiax": "/usr/bin/enumiax"}, "exploitation": {"msfconsole": "/usr/bin/msfconsole", "msfvenom": "/usr/bin/msfvenom", "searchsploit": "/usr/bin/searchsploit", "exploitdb": "/usr/bin/exploitdb", "sqlmap": "/usr/bin/sqlmap", "nikto": "/usr/bin/nikto"}, "lateral_movement": {"impacket-psexec": "/usr/bin/impacket-psexec", "impacket-wmiexec": "/usr/bin/impacket-wmiexec", "impacket-smbexec": "/usr/bin/impacket-smbexec", "impacket-dcomexec": "/usr/bin/impacket-dcomexec", "impacket-atexec": "/usr/bin/impacket-atexec", "impacket-secretsdump": "/usr/bin/impacket-secretsdump", "crackmapexec": "/usr/bin/crackmapexec", "evil-winrm": "/usr/bin/evil-winrm"}, "credential_attacks": {"hydra": "/usr/bin/hydra", "medusa": "/usr/bin/medusa", "patator": "/usr/bin/patator", "ncrack": "/usr/bin/ncrack", "hashcat": "/usr/bin/hashcat", "john": "/usr/bin/john", "kerbrute": "/usr/bin/kerbrute", "responder": "/usr/bin/responder"}, "web_attacks": {"gobuster": "/usr/bin/gobuster", "dirb": "/usr/bin/dirb", "dirbuster": "/usr/bin/dirbuster", "davtest": "/usr/bin/davtest", "uniscan": "/usr/bin/uniscan", "joomscan": "/usr/bin/joomscan", "wpscan": "/usr/bin/wpscan", "ffuf": "/usr/bin/ffuf", "commix": "/usr/bin/commix"}, "pivoting": {"chisel": "/usr/bin/chisel", "ligolo-proxy": "/usr/bin/ligolo-proxy", "ligolo-agent": "/usr/bin/ligolo-agent", "socat": "/usr/bin/socat", "sshuttle": "/usr/bin/sshuttle", "proxychains4": "/usr/bin/proxychains4", "netcat": "/usr/bin/netcat", "nc": "/usr/bin/nc", "ncat": "/usr/bin/ncat"}, "persistence": {"sliver": "/usr/bin/sliver", "powershell-empire": "/usr/bin/powershell-empire", "beef-xss": "/usr/bin/beef-xss", "mimikatz": "/usr/bin/mimikatz", "rubeus": "/usr/bin/rubeus"}, "post_exploitation": {"linpeas": "/usr/bin/linpeas", "winpeas": "/usr/bin/winpeas", "bloodhound": "/usr/bin/bloodhound", "sharphound": "/usr/bin/sharphound", "bloodhound.py": "/usr/bin/bloodhound.py"}, "social_engineering": {"gophish": "/usr/bin/gophish", "social-engineer-toolkit": "/usr/bin/social-engineer-toolkit", "king-phisher": "/usr/bin/king-phisher", "evilginx2": "/usr/bin/evilginx2", "swaks": "/usr/bin/swaks", "sendemail": "/usr/bin/sendemail"}, "evasion": {"veil": "/usr/bin/veil", "shellter": "/usr/bin/shellter", "unicorn": "/usr/bin/unicorn"}, "stealth": {"shred": "/usr/bin/shred", "bleachbit": "/usr/bin/bleachbit"}}