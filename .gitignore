# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/
*.tmp

# Configuration files with sensitive data
config.json
secrets.json
*.key
*.pem
*.crt

# C2 Infrastructure
c2_data/
victim_data/
tor_data/
hidden_service/

# Compiled binaries
*.exe
*.dll
*.so
*.dylib

# Test results
test_results/
coverage/

# Deployment artifacts
deploy/
release/

# Sensitive operational data
credentials/
wordlists/
exploits/
payloads/*.bin
payloads/*.exe

# Tor configuration
torrc
hostname
private_key
